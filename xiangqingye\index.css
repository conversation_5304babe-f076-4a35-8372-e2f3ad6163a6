.page {
	position: relative;
	overflow: hidden;
}

.block_1 {
	background-color: rgba(255, 255, 255, 1);
	max-width: 1200px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
	align-items: center;
}

/* 面包屑导航样式 */
.breadcrumb-nav {
	width: 100%;
	max-width: 1025px;
	margin: 13px auto 0;
}

.breadcrumb-container {
	display: flex;
	align-items: center;
	gap: 6px;
	height: 22px;
}

.breadcrumb-item {
	color: rgba(0, 0, 0, 0.6);
	font-size: 14px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: normal;
	text-decoration: none;
	white-space: nowrap;
	line-height: 22px;
	transition: color 0.3s ease;
}

.breadcrumb-item:hover {
	color: rgba(0, 85, 195, 1);
	text-decoration: underline;
}

.breadcrumb-item:focus {
	outline: 2px solid rgba(0, 85, 195, 0.5);
	outline-offset: 2px;
	border-radius: 2px;
}

.breadcrumb-current {
	color: rgba(0, 0, 0, 0.6);
	font-size: 14px;
	font-family: PingFangSC-Regular, sans-serif;
	font-weight: normal;
	white-space: nowrap;
	line-height: 22px;
}

.breadcrumb-separator {
	width: 18px;
	height: 18px;
	flex-shrink: 0;
	opacity: 0.6;
}

/* 重新设计的区域样式 */
.redesigned-section {
	width: 100%;
	max-width: 1440px;
	margin: 11px auto 0;
	box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.12);
}

/* 上部模块：Banner区域 */
.banner-section {
	background-color: #f2f2f2;
	width: 100%;
	height: 300px;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	box-sizing: border-box;
}

.banner-image {
	max-width: 100%;
	max-height: 100%;
	height: auto;
	width: auto;
	object-fit: contain;
}

/* 下部模块：导航区域 */
.navigation-section {
	background-color: #000000;
	width: 100%;
	height: 82px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-container {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 80px;
}

.nav-item {
	display: flex;
	align-items: center;
	justify-content: center;
}

.nav-text {
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: PingFangSC-Medium, sans-serif;
	font-weight: 500;
	text-align: center;
	white-space: nowrap;
	line-height: 22px;
	cursor: pointer;
	transition: color 0.3s ease;
}

.nav-text:hover {
	color: rgba(255, 255, 255, 0.8);
}

.box_5 {
	background-color: rgba(0, 85, 195, 1);
	width: 72px;
	height: 3px;
	margin: 0 auto;
}

.text_17 {
	width: 871px;
	height: 54px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Light;
	font-weight: 300;
	text-align: center;
	line-height: 27px;
	margin: 38px auto 0;
}

.text_18 {
	width: 119px;
	height: 27px;
	overflow-wrap: break-word;
	color: rgba(236, 41, 20, 1);
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: center;
	white-space: nowrap;
	line-height: 27px;
	margin: 0 auto;
}

.box_6 {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
	background-color: rgba(255, 255, 255, 1);
	border-radius: 8px;
	width: 100%;
	max-width: 1030px;
	height: 838px;
	margin: 42px auto 0;
}

.text_19 {
	width: 71px;
	height: 50px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 50px;
	margin: 17px 0 0 64px;
}

.text_20 {
	width: 315px;
	height: 24px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 24px;
	margin: 14px 0 0 64px;
}

.text-wrapper_4 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 7px 0 0 64px;
}

.text_21 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_1 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_5 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 0 64px;
}

.text_22 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_2 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_6 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 0 64px;
}

.text_23 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_3 {
	width: 903px;
	height: 120px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text_24 {
	width: 315px;
	height: 24px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 24px;
	margin: 11px 0 0 64px;
}

.text-wrapper_7 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 7px 0 0 64px;
}

.text_25 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_4 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_8 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 0 64px;
}

.text_26 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_5 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.text-wrapper_9 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	font-size: 0;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
	margin: 4px 0 20px 64px;
}

.text_27 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.9);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	line-height: 24px;
}

.paragraph_6 {
	width: 903px;
	height: 96px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
}

.image-text_12 {
	width: 111px;
	height: 50px;
	margin: 57px auto 0;
}

.label_1 {
	width: 30px;
	height: 30px;
	margin-top: 13px;
}

.text-group_1 {
	width: 71px;
	height: 50px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 50px;
}

.box_7 {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
	height: 330px;
	width: 100%;
	max-width: 1030px;
	margin: 23px auto 0;
}

.group_3 {
	border-radius: 8px;
	background-image: url(./img/da36ba129190407d9f85de78f5f3456d_mergeImage.png);
	width: 100%;
	max-width: 1030px;
	height: 330px;
}

.image-text_13 {
	width: 201px;
	height: 31px;
	margin: 33px 0 0 603px;
}

.group_4 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_2 {
	width: 183px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.image-text_14 {
	width: 186px;
	height: 31px;
	margin: 16px 0 0 603px;
}

.block_2 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_3 {
	width: 168px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.image-text_15 {
	width: 233px;
	height: 31px;
	margin: 16px 0 0 603px;
}

.box_8 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_4 {
	width: 215px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.image-text_16 {
	width: 170px;
	height: 31px;
	margin: 16px 0 0 603px;
}

.group_5 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_5 {
	width: 152px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.image-text_17 {
	width: 233px;
	height: 31px;
	margin: 16px 0 0 603px;
}

.box_9 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_6 {
	width: 215px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.image-text_18 {
	width: 217px;
	height: 31px;
	margin: 16px 0 31px 603px;
}

.section_1 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_7 {
	width: 199px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.box_10 {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.12);
	background-color: rgba(255, 255, 255, 1);
	border-radius: 8px;
	width: 100%;
	max-width: 1030px;
	height: 169px;
	margin: 20px auto 0;
}

.text-group_13 {
	width: 930px;
	height: 115px;
	margin: 26px 0 0 50px;
}

.text_28 {
	width: 178px;
	height: 27px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 27px;
}

.text_29 {
	width: 930px;
	height: 72px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 0.67);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 24px;
	margin-top: 16px;
}

.image-text_19 {
	width: 111px;
	height: 50px;
	margin: 57px auto 0;
}

.label_2 {
	width: 30px;
	height: 30px;
	margin-top: 13px;
}

.text-group_9 {
	width: 71px;
	height: 50px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 50px;
}

.box_11 {
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
	height: 194px;
	width: 100%;
	max-width: 1030px;
	margin: 23px auto 0;
}

.box_12 {
	background-color: rgba(255, 255, 255, 1);
	border-radius: 8px;
	position: relative;
	width: 100%;
	max-width: 1030px;
	height: 194px;
}

.image-text_20 {
	width: 129px;
	height: 74px;
	margin: 61px 0 0 60px;
}

.group_6 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 54px;
}

.text-group_14 {
	width: 111px;
	height: 74px;
}

.text_30 {
	width: 63px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.text_31 {
	width: 111px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
	margin-top: 12px;
}

.image-text_21 {
	position: absolute;
	left: 60px;
	top: 61px;
	width: 129px;
	height: 74px;
}

.box_13 {
	background-color: rgba(0, 0, 0, 1);
	border-radius: 50%;
	width: 8px;
	height: 8px;
	margin-top: 11px;
}

.text-group_15 {
	width: 111px;
	height: 74px;
}

.text_30 {
	width: 63px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
}

.text_31 {
	width: 111px;
	height: 31px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 31px;
	margin-top: 12px;
}

.box_14 {
	height: 170px;
	background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
		100% no-repeat;
	background-size: 100% 100%;
	width: 100%;
	max-width: 1030px;
	margin: 60px auto 0;
}

.text-wrapper_16 {
	width: 95px;
	height: 33px;
	margin: 26px 0 0 50px;
}

.text_32 {
	width: 95px;
	height: 33px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 24px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 33px;
}

.box_19 {
	width: 859px;
	height: 48px;
	margin: 3px 0 60px 50px;
}

.text_33 {
	width: 639px;
	height: 40px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	line-height: 20px;
	margin-top: 8px;
}

.text-wrapper_11 {
	background-color: rgba(236, 41, 20, 1);
	height: 46px;
	width: 100px;
}

.text_34 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin: 10px 0 0 19px;
}

.box_16 {
	height: 230px;
	background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
		100% no-repeat;
	background-size: 100% 100%;
	width: 1030px;
	position: relative;
	margin: 60px 0 0 445px;
}

.text-wrapper_17 {
	width: 409px;
	height: 22px;
	margin: 18px 0 0 60px;
}

.text_35 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.text_36 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 110px;
}

.text_37 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 110px;
}

.text-wrapper_18 {
	width: 443px;
	height: 22px;
	margin: 12px 0 0 70px;
}

.text_38 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.text_39 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 110px;
}

.text_40 {
	width: 95px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
	margin-left: 112px;
}

.text-wrapper_19 {
	width: 63px;
	height: 22px;
	margin: 12px 0 0 70px;
}

.text_41 {
	width: 63px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.text-wrapper_20 {
	width: 32px;
	height: 22px;
	margin: 12px 0 0 70px;
}

.text_42 {
	width: 32px;
	height: 22px;
	overflow-wrap: break-word;
	color: rgba(255, 255, 255, 1);
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	font-weight: NaN;
	text-align: left;
	white-space: nowrap;
	line-height: 22px;
}

.block_5 {
	width: 178px;
	height: 55px;
	margin: 30px 0 3px 20px;
}

.image-text_22 {
	width: 178px;
	height: 55px;
}

.image_1 {
	width: 56px;
	height: 55px;
}

.text-group_12 {
	width: 118px;
	height: 42px;
	overflow-wrap: break-word;
	color: rgba(0, 0, 0, 1);
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	text-align: left;
	white-space: nowrap;
	line-height: 42px;
	margin-top: 3px;
}

.block_4 {
	background-color: rgba(0, 0, 0, 1);
	position: absolute;
	left: 234px;
	top: 170px;
	width: 1241px;
	height: 60px;
}

/* 重新设计区域的响应式设计 */
@media (max-width: 1200px) {
	.redesigned-section {
		max-width: 100%;
		margin: 11px 20px 0;
	}

	.nav-container {
		gap: 60px;
	}
}

@media (max-width: 900px) {
	.banner-section {
		height: 250px;
		padding: 15px;
	}

	.navigation-section {
		height: 70px;
	}

	.nav-container {
		gap: 40px;
	}

	.nav-text {
		font-size: 15px;
	}
}

@media (max-width: 600px) {
	.redesigned-section {
		margin: 11px 10px 0;
	}

	.banner-section {
		height: 200px;
		padding: 10px;
	}

	.navigation-section {
		height: 60px;
	}

	.nav-container {
		gap: 30px;
	}

	.nav-text {
		font-size: 14px;
	}
}

@media (max-width: 480px) {
	.banner-section {
		height: 180px;
	}

	.nav-container {
		gap: 20px;
	}

	.nav-text {
		font-size: 13px;
	}
}

/* 面包屑响应式设计 */
@media (max-width: 1200px) {
	.breadcrumb-container {
		justify-content: flex-start;
	}
}

@media (max-width: 900px) {
	.breadcrumb-item,
	.breadcrumb-current {
		font-size: 13px;
	}

	.breadcrumb-separator {
		width: 16px;
		height: 16px;
	}
}

@media (max-width: 600px) {
	.breadcrumb-container {
		gap: 4px;
		flex-wrap: wrap;
	}

	.breadcrumb-item,
	.breadcrumb-current {
		font-size: 12px;
		line-height: 20px;
	}

	.breadcrumb-separator {
		width: 14px;
		height: 14px;
	}

	/* 在小屏幕上隐藏中间的面包屑项，只显示首页和当前页 */
	.breadcrumb-container .breadcrumb-item:nth-child(3),
	.breadcrumb-container .breadcrumb-separator:nth-child(4) {
		display: none;
	}

	.breadcrumb-container .breadcrumb-item:nth-child(1)::after {
		content: " ... ";
		color: rgba(0, 0, 0, 0.4);
		margin: 0 4px;
	}
}
